'use client'

import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { Mail, Info, FileText, Shield, ExternalLink } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import ContactModal from '@/components/ContactModal'

interface BlogFooterProps {
  locale: string
}

export default function BlogFooter({ locale }: BlogFooterProps) {
  const t = useTranslations()

  return (
    <footer className="mt-16 bg-slate-900/50 border-t border-slate-700/50 rounded-lg">
      <div className="px-6 py-8">
        {/* 四个板块 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          {/* 联系板块 */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-white flex items-center">
              <Mail className="w-5 h-5 mr-2 text-blue-400" />
              {t('blogFooter.contact.title')}
            </h3>
            <div className="space-y-3 text-sm">
              <p className="text-slate-300">{t('blogFooter.contact.description')}</p>
              <p className="text-slate-400">{t('blogFooter.contact.email')}</p>
              <p className="text-slate-400">{t('blogFooter.contact.response')}</p>
              <div className="pt-2">
                <ContactModal />
              </div>
            </div>
          </div>

          {/* 关于板块 */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-white flex items-center">
              <Info className="w-5 h-5 mr-2 text-green-400" />
              {t('blogFooter.about.title')}
            </h3>
            <div className="space-y-3 text-sm">
              <p className="text-slate-300">{t('blogFooter.about.description')}</p>
              <p className="text-slate-400">{t('blogFooter.about.mission')}</p>
              <p className="text-slate-400">{t('blogFooter.about.vision')}</p>
              <p className="text-slate-400">{t('blogFooter.about.team')}</p>
            </div>
          </div>

          {/* 条款板块 */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-white flex items-center">
              <FileText className="w-5 h-5 mr-2 text-orange-400" />
              {t('blogFooter.terms.title')}
            </h3>
            <div className="space-y-3 text-sm">
              <p className="text-slate-300">{t('blogFooter.terms.description')}</p>
              <p className="text-slate-400">{t('blogFooter.terms.content')}</p>
              <p className="text-slate-400">{t('blogFooter.terms.liability')}</p>
              <p className="text-slate-400">{t('blogFooter.terms.updates')}</p>
              <p className="text-slate-400">{t('blogFooter.terms.contact')}</p>
            </div>
          </div>

          {/* 隐私板块 */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-white flex items-center">
              <Shield className="w-5 h-5 mr-2 text-purple-400" />
              {t('blogFooter.privacy.title')}
            </h3>
            <div className="space-y-3 text-sm">
              <p className="text-slate-300">{t('blogFooter.privacy.description')}</p>
              <p className="text-slate-400">{t('blogFooter.privacy.collection')}</p>
              <p className="text-slate-400">{t('blogFooter.privacy.usage')}</p>
              <p className="text-slate-400">{t('blogFooter.privacy.cookies')}</p>
              <p className="text-slate-400">{t('blogFooter.privacy.rights')}</p>
            </div>
          </div>
        </div>

        <Separator className="my-6 bg-slate-700" />

        {/* 底部信息 */}
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex items-center space-x-2">
            <span className="text-xl">🎆</span>
            <span className="text-lg font-bold text-gradient-goldorange">{t('navigation.title')}</span>
          </div>
          
          <div className="flex items-center space-x-6">
            <Link href={`/${locale}`} className="text-slate-400 hover:text-orange-400 text-sm transition flex items-center">
              <ExternalLink className="w-4 h-4 mr-1" />
              {locale === 'zh' ? '返回主页' : 'Back to Home'}
            </Link>
            <Link href={`/${locale}/sitemap`} className="text-slate-400 hover:text-orange-400 text-sm transition">
              {locale === 'zh' ? '网站地图' : 'Sitemap'}
            </Link>
          </div>
        </div>

        {/* 版权信息 */}
        <div className="text-center mt-6 pt-4 border-t border-slate-700/50">
          <p className="text-slate-400 text-sm">{t('footer.copyright')}</p>
        </div>
      </div>
    </footer>
  )
}
