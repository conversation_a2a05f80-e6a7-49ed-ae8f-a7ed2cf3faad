'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  ArrowLeft,
  Home,
  Calendar,
  Upload,
  MapPin,
  Plane,
  Star,
  Archive,
  TestTube,
  ExternalLink,
  Sparkles,
  Globe,
  Camera,
  Search,
  ArrowUp
} from 'lucide-react'

interface PageInfo {
  title: string
  description: string
  href: string
  icon: React.ReactNode
  status: 'completed' | 'inProgress' | 'planned'
}

export default function SitemapPage() {
  const t = useTranslations()
  const pathname = usePathname()
  const locale = pathname.split('/')[1] || 'zh'
  const [searchTerm, setSearchTerm] = useState('')
  const [showScrollTop, setShowScrollTop] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const pageCategories = {
    core: [
      {
        title: t('sitemap.pages.home.title'),
        description: t('sitemap.pages.home.description'),
        href: `/${locale}`,
        icon: <Home className="w-6 h-6" />,
        status: 'completed' as const
      },
      {
        title: t('sitemap.pages.calendar.title'),
        description: t('sitemap.pages.calendar.description'),
        href: `/${locale}/calendar`,
        icon: <Calendar className="w-6 h-6" />,
        status: 'completed' as const
      },
      {
        title: t('sitemap.pages.submit.title'),
        description: t('sitemap.pages.submit.description'),
        href: `/${locale}/submit`,
        icon: <Upload className="w-6 h-6" />,
        status: 'completed' as const
      }
    ],
    guides: [
      {
        title: t('sitemap.pages.sydneyNyeGuide.title'),
        description: t('sitemap.pages.sydneyNyeGuide.description'),
        href: `/${locale}/sydney-nye-guide`,
        icon: <MapPin className="w-6 h-6" />,
        status: 'completed' as const
      },
      {
        title: t('sitemap.pages.dubaiNyeGuide.title'),
        description: t('sitemap.pages.dubaiNyeGuide.description'),
        href: `/${locale}/dubai-nye-guide`,
        icon: <MapPin className="w-6 h-6" />,
        status: 'completed' as const
      },
      {
        title: t('sitemap.pages.londonNyeGuide.title'),
        description: t('sitemap.pages.londonNyeGuide.description'),
        href: `/${locale}/london-nye-guide`,
        icon: <MapPin className="w-6 h-6" />,
        status: 'completed' as const
      },
      {
        title: t('sitemap.pages.sydneyTravelGuide.title'),
        description: t('sitemap.pages.sydneyTravelGuide.description'),
        href: `/${locale}/sydney-travel-guide`,
        icon: <Plane className="w-6 h-6" />,
        status: 'completed' as const
      },
      {
        title: t('sitemap.pages.dubaiTravelGuide.title'),
        description: t('sitemap.pages.dubaiTravelGuide.description'),
        href: `/${locale}/dubai-travel-guide`,
        icon: <Plane className="w-6 h-6" />,
        status: 'completed' as const
      },
      {
        title: t('sitemap.pages.europeTravelGuide.title'),
        description: t('sitemap.pages.europeTravelGuide.description'),
        href: `/${locale}/europe-travel-guide`,
        icon: <Globe className="w-6 h-6" />,
        status: 'completed' as const
      }
    ],

    archives: [
      {
        title: t('sitemap.pages.archiveDetail1.title'),
        description: t('sitemap.pages.archiveDetail1.description'),
        href: `/${locale}/archive-detail-1`,
        icon: <Archive className="w-6 h-6" />,
        status: 'completed' as const
      },
      {
        title: t('sitemap.pages.archiveDetail2.title'),
        description: t('sitemap.pages.archiveDetail2.description'),
        href: `/${locale}/archive-detail-2`,
        icon: <Archive className="w-6 h-6" />,
        status: 'completed' as const
      }
    ],
    others: [
      {
        title: t('sitemap.pages.test.title'),
        description: t('sitemap.pages.test.description'),
        href: `/${locale}/test`,
        icon: <TestTube className="w-6 h-6" />,
        status: 'completed' as const
      }
    ]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'inProgress':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'planned':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const renderPageCard = (page: PageInfo) => (
    <Card key={page.href} className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-orange-500/20 text-orange-400 group-hover:bg-orange-500/30 transition-colors">
              {page.icon}
            </div>
            <div className="flex-1">
              <CardTitle className="text-white text-lg group-hover:text-orange-400 transition-colors">
                {page.title}
              </CardTitle>
            </div>
          </div>
          <Badge className={`text-xs ${getStatusColor(page.status)}`}>
            {t(`sitemap.status.${page.status}`)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-slate-300 text-sm mb-4 leading-relaxed">
          {page.description}
        </p>
        <Link href={page.href}>
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full bg-transparent border-slate-600 text-slate-300 hover:bg-orange-500/20 hover:border-orange-500/50 hover:text-orange-400 transition-all"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            {t('sitemap.actions.visit')}
          </Button>
        </Link>
      </CardContent>
    </Card>
  )

  // Filter pages based on search term
  const filterPages = (pages: PageInfo[]) => {
    if (!searchTerm) return pages
    return pages.filter(page =>
      page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  const renderCategory = (categoryKey: string, pages: PageInfo[]) => {
    const filteredPages = filterPages(pages)
    if (filteredPages.length === 0) return null

    return (
      <div key={categoryKey} className="mb-12">
        <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
          <span className="w-1 h-8 bg-gradient-to-b from-orange-500 to-red-500 rounded-full mr-4"></span>
          {t(`sitemap.categories.${categoryKey}`)}
          <span className="ml-3 text-sm font-normal text-slate-400">({filteredPages.length})</span>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPages.map(renderPageCard)}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Link href={`/${locale}`}>
                <Button variant="ghost" size="sm" className="text-slate-300 hover:text-orange-400">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {locale === 'zh' ? '返回首页' : 'Back to Home'}
                </Button>
              </Link>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🎆</span>
              <span className="text-xl font-bold text-gradient-goldorange">{t('navigation.title')}</span>
            </div>
            <div className="md:hidden">
              <Link href={`/${locale}/sitemap`}>
                <Button variant="ghost" size="sm" className="text-slate-300 hover:text-orange-400">
                  {locale === 'zh' ? '导航' : 'Menu'}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Page Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              <span className="text-gradient-goldorange">{t('sitemap.title')}</span>
            </h1>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed mb-8">
              {t('sitemap.description')}
            </p>

            {/* Search Box */}
            <div className="max-w-md mx-auto relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
              <Input
                type="text"
                placeholder={locale === 'zh' ? '搜索页面或功能...' : 'Search pages or features...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-800/50 border-slate-700/50 text-white placeholder-slate-400 focus:border-orange-500/50 focus:ring-orange-500/20"
              />
            </div>
          </div>

          {/* Categories */}
          {Object.entries(pageCategories).map(([categoryKey, pages]) =>
            renderCategory(categoryKey, pages)
          )}

          {/* No Results Message */}
          {searchTerm && Object.entries(pageCategories).every(([_, pages]) => filterPages(pages).length === 0) && (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {locale === 'zh' ? '未找到相关页面' : 'No pages found'}
              </h3>
              <p className="text-slate-400">
                {locale === 'zh' ? '尝试使用不同的关键词搜索' : 'Try searching with different keywords'}
              </p>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-slate-900/50 border-t border-slate-700/50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-slate-400 text-sm">
            {locale === 'zh'
              ? '© 2024 全球烟花秀日历. 探索世界最精彩的烟花盛典.'
              : '© 2024 Global Fireworks Calendar. Discover the world\'s most spectacular fireworks celebrations.'
            }
          </p>
        </div>
      </footer>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <Button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 z-50 w-12 h-12 rounded-full bg-orange-500 hover:bg-orange-600 text-white shadow-lg transition-all duration-300"
          size="icon"
        >
          <ArrowUp className="w-5 h-5" />
        </Button>
      )}
    </div>
  )
}
