'use client'

import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import DatePickerTimeline from '@/components/date-picker-timeline'

export default function CalendarPage() {
  const t = useTranslations()
  const pathname = usePathname()
  const locale = pathname.split('/')[1] || 'zh'

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Link href={`/${locale}`}>
                <Button variant="ghost" size="sm" className="text-slate-300 hover:text-orange-400">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {locale === 'zh' ? '返回首页' : 'Back to Home'}
                </Button>
              </Link>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🎆</span>
              <span className="text-xl font-bold text-gradient-goldorange">{t('navigation.title')}</span>
            </div>
          </div>
        </div>
      </nav>

      {/* Calendar Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{locale === 'zh' ? '烟花秀日历' : 'Fireworks Calendar'}</h2>
            <p className="text-xl text-slate-300">{locale === 'zh' ? '查看即将到来的全球烟花活动' : 'View upcoming global fireworks events'}</p>
          </div>
          <div className="bg-slate-800/50 rounded-2xl p-6 backdrop-blur-sm border border-slate-700/50">
            <DatePickerTimeline />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <span className="text-2xl">🎆</span>
              <span className="text-xl font-bold text-gradient-goldorange">{t('navigation.title')}</span>
            </div>
            <p className="text-slate-400 text-sm">{t('footer.copyright')}</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
