'use client'

import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Shield, Eye, Lock, Users, Mail, FileText, AlertCircle, Calendar } from 'lucide-react'
import BlogFooter from '@/components/BlogFooter'

export default function PrivacyPolicyPage() {
  const t = useTranslations()
  const pathname = usePathname()
  const locale = pathname.split('/')[1] || 'zh'

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto py-10 px-4">
        {/* 返回主页按钮 */}
        <div className="mb-6">
          <Link href={`/${locale}`}>
            <button className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 transition">
              {t('privacyPolicy.backToHome')}
            </button>
          </Link>
        </div>

        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('privacyPolicy.title')}
          </h1>
          <p className="text-xl text-gray-600 mb-4">
            {t('privacyPolicy.subtitle')}
          </p>
          <div className="flex items-center justify-center text-gray-500 text-sm">
            <Calendar className="w-4 h-4 mr-2" />
            {t('privacyPolicy.lastUpdated')}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="bg-gray-50 rounded-lg p-8 border border-gray-200">
          <article className="prose prose-invert max-w-none">
            
            {/* 概述 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Shield className="w-6 h-6 mr-2 text-blue-500" />
                {t('privacyPolicy.sections.overview.title')}
              </h2>
              <p className="text-gray-700 text-lg leading-relaxed">
                {t('privacyPolicy.sections.overview.content')}
              </p>
            </section>

            {/* 信息收集 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Eye className="w-6 h-6 mr-2 text-green-500" />
                {t('privacyPolicy.sections.collection.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('privacyPolicy.sections.collection.subtitle')}</p>
              <p className="text-gray-700">{t('privacyPolicy.sections.collection.content')}</p>
            </section>

            {/* 信息使用 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <FileText className="w-6 h-6 mr-2 text-purple-500" />
                {t('privacyPolicy.sections.usage.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('privacyPolicy.sections.usage.subtitle')}</p>
              <p className="text-gray-700">{t('privacyPolicy.sections.usage.content')}</p>
            </section>

            {/* 信息共享 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Users className="w-6 h-6 mr-2 text-orange-500" />
                {t('privacyPolicy.sections.sharing.title')}
              </h2>
              <p className="text-gray-700 mb-4 font-semibold">{t('privacyPolicy.sections.sharing.subtitle')}</p>
              <p className="text-gray-700">{t('privacyPolicy.sections.sharing.content')}</p>
            </section>

            {/* Cookie政策 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <AlertCircle className="w-6 h-6 mr-2 text-yellow-500" />
                {t('privacyPolicy.sections.cookies.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('privacyPolicy.sections.cookies.subtitle')}</p>
              <p className="text-gray-700">{t('privacyPolicy.sections.cookies.content')}</p>
            </section>

            {/* 数据安全 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Lock className="w-6 h-6 mr-2 text-red-500" />
                {t('privacyPolicy.sections.security.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('privacyPolicy.sections.security.subtitle')}</p>
              <p className="text-gray-700">{t('privacyPolicy.sections.security.content')}</p>
            </section>

            {/* 您的权利 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Shield className="w-6 h-6 mr-2 text-indigo-500" />
                {t('privacyPolicy.sections.rights.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('privacyPolicy.sections.rights.subtitle')}</p>
              <p className="text-gray-700">{t('privacyPolicy.sections.rights.content')}</p>
            </section>

            {/* 联系我们 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Mail className="w-6 h-6 mr-2 text-pink-500" />
                {t('privacyPolicy.sections.contact.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('privacyPolicy.sections.contact.content')}</p>
              <p className="text-gray-700 mb-2">{t('privacyPolicy.sections.contact.email')}</p>
              <p className="text-gray-700">{t('privacyPolicy.sections.contact.response')}</p>
            </section>

            {/* 政策更新 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Calendar className="w-6 h-6 mr-2 text-teal-500" />
                {t('privacyPolicy.sections.updates.title')}
              </h2>
              <p className="text-gray-700">{t('privacyPolicy.sections.updates.content')}</p>
            </section>

          </article>
        </div>

        {/* 博客页脚 */}
        <BlogFooter locale={locale} />
      </div>
    </div>
  )
}
