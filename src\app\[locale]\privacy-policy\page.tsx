'use client'

import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Shield, Eye, Lock, Users, Mail, FileText, AlertCircle, Calendar } from 'lucide-react'
import BlogFooter from '@/components/BlogFooter'

export default function PrivacyPolicyPage() {
  const t = useTranslations()
  const pathname = usePathname()
  const locale = pathname.split('/')[1] || 'zh'

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="max-w-4xl mx-auto py-10 px-4">
        {/* 返回主页按钮 */}
        <div className="mb-6">
          <Link href={`/${locale}`}>
            <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">
              {t('privacyPolicy.backToHome')}
            </button>
          </Link>
        </div>

        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {t('privacyPolicy.title')}
          </h1>
          <p className="text-xl text-slate-300 mb-4">
            {t('privacyPolicy.subtitle')}
          </p>
          <div className="flex items-center justify-center text-slate-400 text-sm">
            <Calendar className="w-4 h-4 mr-2" />
            {t('privacyPolicy.lastUpdated')}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="bg-slate-800/50 rounded-lg p-8 backdrop-blur-sm border border-slate-700/50">
          <article className="prose prose-invert max-w-none">
            
            {/* 概述 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <Shield className="w-6 h-6 mr-2 text-blue-400" />
                {t('privacyPolicy.sections.overview.title')}
              </h2>
              <p className="text-black text-lg leading-relaxed">
                {t('privacyPolicy.sections.overview.content')}
              </p>
            </section>

            {/* 信息收集 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <Eye className="w-6 h-6 mr-2 text-green-400" />
                {t('privacyPolicy.sections.collection.title')}
              </h2>
              <p className="text-black mb-4">{t('privacyPolicy.sections.collection.subtitle')}</p>
              <p className="text-black">{t('privacyPolicy.sections.collection.content')}</p>
            </section>

            {/* 信息使用 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <FileText className="w-6 h-6 mr-2 text-purple-400" />
                {t('privacyPolicy.sections.usage.title')}
              </h2>
              <p className="text-black mb-4">{t('privacyPolicy.sections.usage.subtitle')}</p>
              <p className="text-black">{t('privacyPolicy.sections.usage.content')}</p>
            </section>

            {/* 信息共享 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <Users className="w-6 h-6 mr-2 text-orange-400" />
                {t('privacyPolicy.sections.sharing.title')}
              </h2>
              <p className="text-black mb-4 font-semibold">{t('privacyPolicy.sections.sharing.subtitle')}</p>
              <p className="text-black">{t('privacyPolicy.sections.sharing.content')}</p>
            </section>

            {/* Cookie政策 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <AlertCircle className="w-6 h-6 mr-2 text-yellow-400" />
                {t('privacyPolicy.sections.cookies.title')}
              </h2>
              <p className="text-black mb-4">{t('privacyPolicy.sections.cookies.subtitle')}</p>
              <p className="text-black">{t('privacyPolicy.sections.cookies.content')}</p>
            </section>

            {/* 数据安全 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <Lock className="w-6 h-6 mr-2 text-red-400" />
                {t('privacyPolicy.sections.security.title')}
              </h2>
              <p className="text-black mb-4">{t('privacyPolicy.sections.security.subtitle')}</p>
              <p className="text-black">{t('privacyPolicy.sections.security.content')}</p>
            </section>

            {/* 您的权利 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <Shield className="w-6 h-6 mr-2 text-indigo-400" />
                {t('privacyPolicy.sections.rights.title')}
              </h2>
              <p className="text-black mb-4">{t('privacyPolicy.sections.rights.subtitle')}</p>
              <p className="text-black">{t('privacyPolicy.sections.rights.content')}</p>
            </section>

            {/* 联系我们 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <Mail className="w-6 h-6 mr-2 text-pink-400" />
                {t('privacyPolicy.sections.contact.title')}
              </h2>
              <p className="text-black mb-4">{t('privacyPolicy.sections.contact.content')}</p>
              <p className="text-black mb-2">{t('privacyPolicy.sections.contact.email')}</p>
              <p className="text-black">{t('privacyPolicy.sections.contact.response')}</p>
            </section>

            {/* 政策更新 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-white">
                <Calendar className="w-6 h-6 mr-2 text-teal-400" />
                {t('privacyPolicy.sections.updates.title')}
              </h2>
              <p className="text-black">{t('privacyPolicy.sections.updates.content')}</p>
            </section>

          </article>
        </div>

        {/* 博客页脚 */}
        <BlogFooter locale={locale} />
      </div>
    </div>
  )
}
