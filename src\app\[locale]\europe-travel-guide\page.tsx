'use client'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star, Navigation, AlertCircle, Ticket, Plane, Route } from 'lucide-react'
import { usePathname } from 'next/navigation'
import BlogFooter from '@/components/BlogFooter'

export default function EuropeTravelGuide() {
  const t = useTranslations();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'zh';
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('europeTravelGuide.backToHome')}</button>
        </Link>
      </div>

      {/* 标题和简介 */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold mb-4 leading-tight">{t('europeTravelGuide.title')}</h1>
        <p className="text-black mb-6 text-base md:text-lg">{t('europeTravelGuide.subtitle')}</p>
        
        {/* 头图 */}
        <div className="relative w-full h-64 md:h-80 mb-6 rounded-lg overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1467269204594-9661b134dd2b?w=800&h=400&fit=crop"
            alt={t('europeTravelGuide.title')}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <div className="absolute bottom-4 left-4 text-white">
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {t('europeTravelGuide.duration')}
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {t('europeTravelGuide.cities')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            {/* 简介 */}
            <div className="mb-8">
              <p className="text-lg text-black mb-4">{t('europeTravelGuide.intro')}</p>
            </div>

            {/* 城市概述 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Star className="w-6 h-6 mr-2 text-yellow-400" />
                {t('europeTravelGuide.cities.title')}
              </h2>
              
              <div className="overflow-x-auto">
                <table className="w-full bg-slate-800/50 rounded-lg overflow-hidden">
                  <thead className="bg-slate-700/50">
                    <tr>
                      <th className="px-4 py-3 text-left text-white font-bold">{t('europeTravelGuide.cities.table.city')}</th>
                      <th className="px-4 py-3 text-left text-white font-bold">{t('europeTravelGuide.cities.table.highlight')}</th>
                      <th className="px-4 py-3 text-left text-white font-bold">{t('europeTravelGuide.cities.table.culture')}</th>
                    </tr>
                  </thead>
                  <tbody className="text-black">
                    <tr className="border-b border-slate-700/50">
                      <td className="px-4 py-3 font-medium text-blue-400">{t('europeTravelGuide.cities.reykjavik.name')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.reykjavik.highlight')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.reykjavik.culture')}</td>
                    </tr>
                    <tr className="border-b border-slate-700/50">
                      <td className="px-4 py-3 font-medium text-green-400">{t('europeTravelGuide.cities.edinburgh.name')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.edinburgh.highlight')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.edinburgh.culture')}</td>
                    </tr>
                    <tr className="border-b border-slate-700/50">
                      <td className="px-4 py-3 font-medium text-red-400">{t('europeTravelGuide.cities.berlin.name')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.berlin.highlight')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.berlin.culture')}</td>
                    </tr>
                    <tr className="border-b border-slate-700/50">
                      <td className="px-4 py-3 font-medium text-purple-400">{t('europeTravelGuide.cities.paris.name')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.paris.highlight')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.paris.culture')}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 font-medium text-orange-400">{t('europeTravelGuide.cities.lisbon.name')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.lisbon.highlight')}</td>
                      <td className="px-4 py-3 text-sm">{t('europeTravelGuide.cities.lisbon.culture')}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </section>

            {/* 10-14天行程参考 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Route className="w-6 h-6 mr-2 text-blue-400" />
                {t('europeTravelGuide.itinerary.title')}
              </h2>
              
              <div className="space-y-4">
                {/* Day 1-3: Reykjavík */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-blue-400">{t('europeTravelGuide.itinerary.reykjavik.title')}</h3>
                  <ul className="text-black space-y-2">
                    <li className="flex items-start">
                      <span className="text-blue-400 mr-2">•</span>
                      {t('europeTravelGuide.itinerary.reykjavik.activity1')}
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-400 mr-2">•</span>
                      {t('europeTravelGuide.itinerary.reykjavik.activity2')}
                    </li>
                  </ul>
                </div>

                {/* Day 4-6: Edinburgh */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-green-400">{t('europeTravelGuide.itinerary.edinburgh.title')}</h3>
                  <ul className="text-black space-y-2">
                    <li className="flex items-start">
                      <span className="text-green-400 mr-2">•</span>
                      {t('europeTravelGuide.itinerary.edinburgh.activity1')}
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-400 mr-2">•</span>
                      {t('europeTravelGuide.itinerary.edinburgh.activity2')}
                    </li>
                  </ul>
                </div>

                {/* Day 7-9: Berlin */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-red-400">{t('europeTravelGuide.itinerary.berlin.title')}</h3>
                  <ul className="text-black space-y-2">
                    <li className="flex items-start">
                      <span className="text-red-400 mr-2">•</span>
                      {t('europeTravelGuide.itinerary.berlin.activity1')}
                    </li>
                  </ul>
                </div>

                {/* Day 10-12: Paris */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-purple-400">{t('europeTravelGuide.itinerary.paris.title')}</h3>
                  <ul className="text-black space-y-2">
                    <li className="flex items-start">
                      <span className="text-purple-400 mr-2">•</span>
                      {t('europeTravelGuide.itinerary.paris.activity1')}
                    </li>
                  </ul>
                </div>

                {/* Day 13-14: Lisbon */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-orange-400">{t('europeTravelGuide.itinerary.lisbon.title')}</h3>
                  <ul className="text-black space-y-2">
                    <li className="flex items-start">
                      <span className="text-orange-400 mr-2">•</span>
                      {t('europeTravelGuide.itinerary.lisbon.activity1')}
                    </li>
                  </ul>
                </div>
              </div>
            </section>

            {/* 交通建议 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Plane className="w-6 h-6 mr-2 text-blue-400" />
                {t('europeTravelGuide.transport.title')}
              </h2>
              
              <div className="space-y-4">
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-blue-400">{t('europeTravelGuide.transport.intercity.title')}</h3>
                  <p className="text-black">{t('europeTravelGuide.transport.intercity.description')}</p>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-green-400">{t('europeTravelGuide.transport.europe.title')}</h3>
                  <p className="text-black">{t('europeTravelGuide.transport.europe.description')}</p>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-orange-400">{t('europeTravelGuide.transport.local.title')}</h3>
                  <p className="text-black">{t('europeTravelGuide.transport.local.description')}</p>
                </div>
              </div>
            </section>

            {/* 出行贴士 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <AlertCircle className="w-6 h-6 mr-2 text-yellow-400" />
                {t('europeTravelGuide.tips.title')}
              </h2>
              
              <div className="space-y-4">
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-blue-400">{t('europeTravelGuide.tips.iceland.title')}</h3>
                  <p className="text-black">{t('europeTravelGuide.tips.iceland.description')}</p>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-green-400">{t('europeTravelGuide.tips.edinburgh.title')}</h3>
                  <p className="text-black">{t('europeTravelGuide.tips.edinburgh.description')}</p>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-red-400">{t('europeTravelGuide.tips.berlin.title')}</h3>
                  <p className="text-black">{t('europeTravelGuide.tips.berlin.description')}</p>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-purple-400">{t('europeTravelGuide.tips.paris.title')}</h3>
                  <p className="text-black">{t('europeTravelGuide.tips.paris.description')}</p>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-orange-400">{t('europeTravelGuide.tips.lisbon.title')}</h3>
                  <p className="text-black">{t('europeTravelGuide.tips.lisbon.description')}</p>
                </div>
              </div>
            </section>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 快速信息 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('europeTravelGuide.quickInfo.title')}</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-blue-400" />
                  <span className="text-black">{t('europeTravelGuide.quickInfo.duration')}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-green-400" />
                  <span className="text-black">{t('europeTravelGuide.quickInfo.cities')}</span>
                </div>
                <div className="flex items-center">
                  <Plane className="w-4 h-4 mr-2 text-red-400" />
                  <span className="text-black">{t('europeTravelGuide.quickInfo.transport')}</span>
                </div>
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-2 text-purple-400" />
                  <span className="text-black">{t('europeTravelGuide.quickInfo.type')}</span>
                </div>
                <div className="flex items-center">
                  <Ticket className="w-4 h-4 mr-2 text-yellow-400" />
                  <span className="text-black">{t('europeTravelGuide.quickInfo.budget')}</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('europeTravelGuide.relatedLinks.title')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/sydney-travel-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('europeTravelGuide.relatedLinks.sydney')}</span>
                </Link>
                <Link href={`/${locale}/dubai-travel-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('europeTravelGuide.relatedLinks.dubai')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 博客页脚 */}
      <BlogFooter locale={locale} />
    </div>
  );
}
