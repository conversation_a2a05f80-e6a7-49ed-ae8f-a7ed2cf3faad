'use client'

import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { FileText, CheckCircle, Users, Shield, AlertTriangle, Globe, Calendar, Mail, Gavel, Server } from 'lucide-react'
import BlogFooter from '@/components/BlogFooter'

export default function TermsOfServicePage() {
  const t = useTranslations()
  const pathname = usePathname()
  const locale = pathname.split('/')[1] || 'zh'

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto py-10 px-4">
        {/* 返回主页按钮 */}
        <div className="mb-6">
          <Link href={`/${locale}`}>
            <button className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 transition">
              {t('termsOfService.backToHome')}
            </button>
          </Link>
        </div>

        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('termsOfService.title')}
          </h1>
          <p className="text-xl text-gray-600 mb-4">
            {t('termsOfService.subtitle')}
          </p>
          <div className="flex items-center justify-center text-gray-500 text-sm">
            <Calendar className="w-4 h-4 mr-2" />
            {t('termsOfService.lastUpdated')}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="bg-gray-50 rounded-lg p-8 border border-gray-200">
          <article className="prose prose-invert max-w-none">
            
            {/* 条款接受 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <CheckCircle className="w-6 h-6 mr-2 text-green-500" />
                {t('termsOfService.sections.acceptance.title')}
              </h2>
              <p className="text-gray-700 text-lg leading-relaxed">
                {t('termsOfService.sections.acceptance.content')}
              </p>
            </section>

            {/* 服务描述 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Globe className="w-6 h-6 mr-2 text-blue-500" />
                {t('termsOfService.sections.description.title')}
              </h2>
              <p className="text-gray-700">{t('termsOfService.sections.description.content')}</p>
            </section>

            {/* 用户责任 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Users className="w-6 h-6 mr-2 text-purple-500" />
                {t('termsOfService.sections.userResponsibilities.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('termsOfService.sections.userResponsibilities.subtitle')}</p>
              <p className="text-gray-700">{t('termsOfService.sections.userResponsibilities.content')}</p>
            </section>

            {/* 内容使用 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <FileText className="w-6 h-6 mr-2 text-orange-500" />
                {t('termsOfService.sections.content.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('termsOfService.sections.content.subtitle')}</p>
              <p className="text-gray-700">{t('termsOfService.sections.content.content')}</p>
            </section>

            {/* 免责声明 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <AlertTriangle className="w-6 h-6 mr-2 text-red-500" />
                {t('termsOfService.sections.disclaimer.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('termsOfService.sections.disclaimer.subtitle')}</p>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-gray-700">{t('termsOfService.sections.disclaimer.content')}</p>
              </div>
            </section>

            {/* 服务可用性 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Server className="w-6 h-6 mr-2 text-cyan-500" />
                {t('termsOfService.sections.availability.title')}
              </h2>
              <p className="text-gray-700">{t('termsOfService.sections.availability.content')}</p>
            </section>

            {/* 隐私保护 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Shield className="w-6 h-6 mr-2 text-indigo-500" />
                {t('termsOfService.sections.privacy.title')}
              </h2>
              <p className="text-gray-700">
                {t('termsOfService.sections.privacy.content')}
                <Link href={`/${locale}/privacy-policy`} className="text-blue-600 hover:text-blue-700 underline ml-1">
                  隐私政策
                </Link>
              </p>
            </section>

            {/* 条款修改 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <FileText className="w-6 h-6 mr-2 text-yellow-500" />
                {t('termsOfService.sections.modifications.title')}
              </h2>
              <p className="text-gray-700">{t('termsOfService.sections.modifications.content')}</p>
            </section>

            {/* 服务终止 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <AlertTriangle className="w-6 h-6 mr-2 text-pink-500" />
                {t('termsOfService.sections.termination.title')}
              </h2>
              <p className="text-gray-700">{t('termsOfService.sections.termination.content')}</p>
            </section>

            {/* 适用法律 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Gavel className="w-6 h-6 mr-2 text-emerald-500" />
                {t('termsOfService.sections.governing.title')}
              </h2>
              <p className="text-gray-700">{t('termsOfService.sections.governing.content')}</p>
            </section>

            {/* 联系我们 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-gray-900">
                <Mail className="w-6 h-6 mr-2 text-teal-500" />
                {t('termsOfService.sections.contact.title')}
              </h2>
              <p className="text-gray-700 mb-4">{t('termsOfService.sections.contact.content')}</p>
              <p className="text-gray-700 mb-2">{t('termsOfService.sections.contact.email')}</p>
              <p className="text-gray-700">{t('termsOfService.sections.contact.response')}</p>
            </section>

          </article>
        </div>

        {/* 博客页脚 */}
        <BlogFooter locale={locale} />
      </div>
    </div>
  )
}
